version: '3.8'

services:
  shadowsocks:
    build:
      context: .
      dockerfile: Dockerfile
    image: shadowsocks-server
    container_name: shadowsocks
    ports:
      - "9443:9443"
    volumes:
      - ./config.json:/etc/shadowsocks-libev/config.json
    restart: unless-stopped
    environment:
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - proxy-network

networks:
  proxy-network:
    driver: bridge